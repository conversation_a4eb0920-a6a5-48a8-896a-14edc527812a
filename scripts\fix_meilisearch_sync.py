#!/usr/bin/env python3
"""
MeiliSearch 同步修复脚本
用于清理失败的任务并重新同步数据
"""

import asyncio
import aiohttp
import logging
import sys
import os
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.utils.config import settings
from app.core.tortoise_config import init_db, close_db
from app.models.resource import PanResource
from app.services.intelligent_batch_manager import intelligent_batch_manager, Priority

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# MeiliSearch 配置
meili_conf = settings.get("meilisearch", {})
MEILI_HOST = meili_conf.get("host", "http://127.0.0.1:7700")
MEILI_API_KEY = meili_conf.get("api_key", "masterKey")
INDEX_NAME = meili_conf.get("index_name", "resources")


async def get_failed_tasks():
    """获取所有失败的 MeiliSearch 任务"""
    async with aiohttp.ClientSession() as session:
        headers = {"Authorization": f"Bearer {MEILI_API_KEY}"}
        url = f"{MEILI_HOST}/tasks?statuses=failed&limit=1000"

        try:
            async with session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get("results", [])
                else:
                    logger.error(f"获取失败任务失败: HTTP {response.status}")
                    return []
        except Exception as e:
            logger.error(f"获取失败任务异常: {e}")
            return []


async def clear_failed_tasks():
    """清理所有失败的任务"""
    async with aiohttp.ClientSession() as session:
        headers = {"Authorization": f"Bearer {MEILI_API_KEY}"}

        # 获取所有失败的任务
        failed_tasks = await get_failed_tasks()

        if not failed_tasks:
            logger.info("没有找到失败的任务")
            return

        logger.info(f"找到 {len(failed_tasks)} 个失败的任务")

        # 删除失败的任务（通过删除并重新创建索引）
        # 注意：这会清空所有数据，需要重新同步
        try:
            # 删除索引
            url = f"{MEILI_HOST}/indexes/{INDEX_NAME}"
            async with session.delete(url, headers=headers) as response:
                if response.status in [200, 202]:
                    logger.info("成功删除索引")
                else:
                    logger.warning(f"删除索引失败: HTTP {response.status}")

            # 重新创建索引
            url = f"{MEILI_HOST}/indexes"
            data = {
                "uid": INDEX_NAME,
                "primaryKey": "id",  # 使用 id 作为主键，与现有配置一致
            }
            async with session.post(url, json=data, headers=headers) as response:
                if response.status in [200, 201, 202]:
                    logger.info("成功重新创建索引")
                else:
                    logger.error(f"重新创建索引失败: HTTP {response.status}")

        except Exception as e:
            logger.error(f"清理失败任务异常: {e}")


async def resync_failed_data():
    """重新同步失败的数据"""
    logger.info("开始重新同步数据...")

    # 初始化数据库
    await init_db()

    try:
        # 获取最近的资源数据进行重新同步
        # 这里同步最近 7 天的数据，你可以根据需要调整
        from datetime import timedelta

        cutoff_date = datetime.now() - timedelta(days=7)

        resources = await PanResource.filter(created_at__gte=cutoff_date).values()

        logger.info(f"找到 {len(resources)} 个资源需要重新同步")

        # 分批处理
        batch_size = 1000
        total_processed = 0

        for i in range(0, len(resources), batch_size):
            batch = resources[i : i + batch_size]
            logger.info(f"处理批次 {i//batch_size + 1}: {len(batch)} 个资源")

            # 使用智能批量管理器处理
            for resource in batch:
                success = await intelligent_batch_manager.add_operation(
                    operation_type="UPDATE",
                    resource_key=resource["resource_key"],
                    priority=Priority.HIGH,
                    user_triggered=True,
                )

                if success:
                    total_processed += 1
                else:
                    logger.warning(f"添加操作失败: {resource['resource_key']}")

            # 等待批次处理完成
            await asyncio.sleep(2)

        # 强制处理所有待处理的操作
        logger.info("等待所有操作处理完成...")
        await intelligent_batch_manager.force_process()

        logger.info(f"重新同步完成，总计处理 {total_processed} 个资源")

    finally:
        await close_db()


async def check_index_status():
    """检查索引状态"""
    async with aiohttp.ClientSession() as session:
        headers = {"Authorization": f"Bearer {MEILI_API_KEY}"}
        url = f"{MEILI_HOST}/indexes/{INDEX_NAME}"

        try:
            async with session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"索引状态:")
                    logger.info(f"  文档数量: {data.get('numberOfDocuments', 0):,}")
                    logger.info(f"  主键: {data.get('primaryKey', 'unknown')}")
                    logger.info(f"  创建时间: {data.get('createdAt', 'unknown')}")
                    logger.info(f"  更新时间: {data.get('updatedAt', 'unknown')}")
                    return True
                else:
                    logger.error(f"获取索引状态失败: HTTP {response.status}")
                    return False
        except Exception as e:
            logger.error(f"检查索引状态异常: {e}")
            return False


async def main():
    """主函数"""
    logger.info("🚀 开始 MeiliSearch 同步修复")

    # 1. 检查当前索引状态
    logger.info("1. 检查当前索引状态...")
    await check_index_status()

    # 2. 获取失败任务信息
    logger.info("2. 获取失败任务信息...")
    failed_tasks = await get_failed_tasks()
    logger.info(f"发现 {len(failed_tasks)} 个失败的任务")

    if failed_tasks:
        # 显示失败任务的详细信息
        for i, task in enumerate(failed_tasks[:5]):  # 只显示前5个
            error = task.get("error", {})
            logger.info(f"  任务 {i+1}: {error.get('message', 'Unknown error')}")

    # 3. 清理失败任务
    logger.info("3. 清理失败任务...")
    await clear_failed_tasks()

    # 4. 重新同步数据
    logger.info("4. 重新同步数据...")
    await resync_failed_data()

    # 5. 检查最终状态
    logger.info("5. 检查最终状态...")
    await check_index_status()

    logger.info("✅ MeiliSearch 同步修复完成")


if __name__ == "__main__":
    asyncio.run(main())
